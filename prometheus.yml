# Prometheus Configuration for VM Environment
global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'vm-production'
    environment: 'corporate'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# Load rules once and periodically evaluate them
rule_files:
  - "alert_rules.yml"

# Scrape configuration
scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Node Exporter for VM system metrics
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

  # cAdvisor for container metrics
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s

  # Your Java Applications
  - job_name: 'java-app-8007'
    static_configs:
      - targets: ['host.docker.internal:8007']
    metrics_path: '/actuator/prometheus'  # Spring Boot Actuator
    scrape_interval: 30s

  - job_name: 'java-app-9899'
    static_configs:
      - targets: ['host.docker.internal:9899']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'java-app-5555'
    static_configs:
      - targets: ['host.docker.internal:5555']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # SMS Gateway monitoring
  - job_name: 'sms-gateway'
    static_configs:
      - targets: ['host.docker.internal:7788']
    metrics_path: '/metrics'
    scrape_interval: 60s

  # Bearer Services
  - job_name: 'bearer-service-1'
    static_configs:
      - targets: ['host.docker.internal:13000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'bearer-service-2'
    static_configs:
      - targets: ['host.docker.internal:13001']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Alloy Integration
  - job_name: 'alloy'
    static_configs:
      - targets: ['host.docker.internal:12345']
    scrape_interval: 30s

  # Loki for logs (if running)
  - job_name: 'loki'
    static_configs:
      - targets: ['host.docker.internal:3100']
    scrape_interval: 60s

  # PostgreSQL Database (requires postgres_exporter)
  - job_name: 'postgres-main'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s

  # Blackbox exporter for endpoint monitoring
  - job_name: 'blackbox-http'
    metrics_path: /probe
    params:
      module: [http_2xx]
    static_configs:
      - targets:
        - http://host.docker.internal:8007/health
        - http://host.docker.internal:9899/health
        - http://host.docker.internal:5555/status
        - http://host.docker.internal:7788/status
        - http://host.docker.internal:13000/health
        - http://host.docker.internal:13001/health
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox-exporter:9115

  # TCP endpoint monitoring
  - job_name: 'blackbox-tcp'
    metrics_path: /probe
    params:
      module: [tcp_connect]
    static_configs:
      - targets:
        - host.docker.internal:8007
        - host.docker.internal:9899
        - host.docker.internal:5555
        - host.docker.internal:7788
        - host.docker.internal:13000
        - host.docker.internal:13001
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox-exporter:9115

  # Custom business metrics (if you expose them)
  - job_name: 'business-metrics'
    static_configs:
      - targets: ['host.docker.internal:8080']  # Your custom metrics endpoint
    metrics_path: '/business/metrics'
    scrape_interval: 60s
