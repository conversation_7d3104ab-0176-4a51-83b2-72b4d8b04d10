# Core data science libraries
pandas>=1.5.0
numpy>=1.21.0
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.0.0

# Financial analysis libraries
yfinance>=0.2.0
pandas-ta>=0.3.14b
TA-Lib>=0.4.25

# NLP and sentiment analysis
nltk>=3.8
textblob>=0.17.1
vaderSentiment>=3.3.2
transformers>=4.20.0
torch>=1.12.0

# Text processing
wordcloud>=1.9.0
spacy>=3.4.0

# Statistical analysis
scipy>=1.9.0
scikit-learn>=1.1.0
statsmodels>=0.13.0

# Jupyter and development
jupyter>=1.0.0
ipykernel>=6.15.0
notebook>=6.4.0

# Data visualization
plotly-dash>=2.6.0
bokeh>=2.4.0

# Utilities
python-dotenv>=0.20.0
tqdm>=4.64.0
requests>=2.28.0

# Testing
pytest>=7.1.0
pytest-cov>=3.0.0

# Code quality
black>=22.6.0
flake8>=5.0.0
isort>=5.10.0
